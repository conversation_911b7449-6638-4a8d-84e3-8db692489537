<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Storage;
use PhpParser\Node\Expr\FuncCall;
use Session;
use function PHPUnit\Framework\returnArgument;

class DemoController extends Controller
{
    public function token(Request $request) {
        $token = csrf_token();
        $username = $request->input('username','John');
        return view('demo',['csrf_token'=>$token,'name'=>$username]);
    }
    public function index(Request $request)
    {
		$template = $request->input('name');
        return Blade::render("Hello, $template");
    }

    public function uploadFile(Request $request) {
        $file = $request->file('avatar');
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();
        $file->storeAs('temp/',$name.'.'.$extension);
    }

    public function move(Request $request) {
        $file = $request->input("filename");
        $file_to = $request->input("to");
        rename(Storage::disk('local')->path('').$file, Storage::disk('local')->path('').$file_to);
    }
}
