.codehilite .hll { background-color: #222222 }
.codehilite .c { color: #999 } /* Comment */
.codehilite .err { color: #cccccc } /* Error */
.codehilite .g { color: #cccccc } /* Generic */
.codehilite .k { color: #cdcd00 } /* Keyword */
.codehilite .l { color: #cccccc } /* Literal */
.codehilite .n { color: #cccccc } /* Name */
.codehilite .o { color: #3399cc } /* Operator */
.codehilite .x { color: #cccccc } /* Other */
.codehilite .p { color: #cccccc } /* Punctuation */
.codehilite .cm { color: #999 } /* Comment.Multiline */
.codehilite .cp { color: #999 } /* Comment.Preproc */
.codehilite .c1 { color: #999 } /* Comment.Single */
.codehilite .cs { color: #cd0000; font-weight: bold } /* Comment.Special */
.codehilite .gd { color: #cd0000 } /* Generic.Deleted */
.codehilite .ge { color: #cccccc; font-style: italic } /* Generic.Emph */
.codehilite .gr { color: #FF0000 } /* Generic.Error */
.codehilite .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.codehilite .gi { color: #00cd00 } /* Generic.Inserted */
.codehilite .go { color: #808080 } /* Generic.Output */
.codehilite .gp { color: #000080; font-weight: bold } /* Generic.Prompt */
.codehilite .gs { color: #cccccc; font-weight: bold } /* Generic.Strong */
.codehilite .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.codehilite .gt { color: #0040D0 } /* Generic.Traceback */
.codehilite .kc { color: #cdcd00 } /* Keyword.Constant */
.codehilite .kd { color: #00cd00 } /* Keyword.Declaration */
.codehilite .kn { color: #cd00cd } /* Keyword.Namespace */
.codehilite .kp { color: #cdcd00 } /* Keyword.Pseudo */
.codehilite .kr { color: #cdcd00 } /* Keyword.Reserved */
.codehilite .kt { color: #00cd00 } /* Keyword.Type */
.codehilite .ld { color: #cccccc } /* Literal.Date */
.codehilite .m { color: #cd00cd } /* Literal.Number */
.codehilite .s { color: #cd0000 } /* Literal.String */
.codehilite .na { color: #cccccc } /* Name.Attribute */
.codehilite .nb { color: #cd00cd } /* Name.Builtin */
.codehilite .nc { color: #00cdcd } /* Name.Class */
.codehilite .no { color: #cccccc } /* Name.Constant */
.codehilite .nd { color: #cccccc } /* Name.Decorator */
.codehilite .ni { color: #cccccc } /* Name.Entity */
.codehilite .ne { color: #666699; font-weight: bold } /* Name.Exception */
.codehilite .nf { color: #cccccc } /* Name.Function */
.codehilite .nl { color: #cccccc } /* Name.Label */
.codehilite .nn { color: #cccccc } /* Name.Namespace */
.codehilite .nx { color: #cccccc } /* Name.Other */
.codehilite .py { color: #cccccc } /* Name.Property */
.codehilite .nt { color: #cccccc } /* Name.Tag */
.codehilite .nv { color: #00cdcd } /* Name.Variable */
.codehilite .ow { color: #cdcd00 } /* Operator.Word */
.codehilite .w { color: #cccccc } /* Text.Whitespace */
.codehilite .mf { color: #cd00cd } /* Literal.Number.Float */
.codehilite .mh { color: #cd00cd } /* Literal.Number.Hex */
.codehilite .mi { color: #cd00cd } /* Literal.Number.Integer */
.codehilite .mo { color: #cd00cd } /* Literal.Number.Oct */
.codehilite .sb { color: #cd0000 } /* Literal.String.Backtick */
.codehilite .sc { color: #cd0000 } /* Literal.String.Char */
.codehilite .sd { color: #cd0000 } /* Literal.String.Doc */
.codehilite .s2 { color: #cd0000 } /* Literal.String.Double */
.codehilite .se { color: #cd0000 } /* Literal.String.Escape */
.codehilite .sh { color: #cd0000 } /* Literal.String.Heredoc */
.codehilite .si { color: #cd0000 } /* Literal.String.Interpol */
.codehilite .sx { color: #cd0000 } /* Literal.String.Other */
.codehilite .sr { color: #cd0000 } /* Literal.String.Regex */
.codehilite .s1 { color: #cd0000 } /* Literal.String.Single */
.codehilite .ss { color: #cd0000 } /* Literal.String.Symbol */
.codehilite .bp { color: #cd00cd } /* Name.Builtin.Pseudo */
.codehilite .vc { color: #00cdcd } /* Name.Variable.Class */
.codehilite .vg { color: #00cdcd } /* Name.Variable.Global */
.codehilite .vi { color: #00cdcd } /* Name.Variable.Instance */
.codehilite .il { color: #cd00cd } /* Literal.Number.Integer.Long */
