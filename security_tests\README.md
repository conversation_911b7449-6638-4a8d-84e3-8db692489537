# Laravel 应用安全测试工具集

这是一套针对Laravel应用安全漏洞的概念验证(PoC)测试工具，**仅用于授权的安全测试目的**。

## ⚠️ 重要声明

- 这些工具仅用于授权的安全测试和漏洞研究
- 请勿在未经授权的系统上使用这些工具
- 使用者需对使用这些工具的后果承担全部责任
- 建议在隔离的测试环境中使用

## 🔍 支持的漏洞测试

### 1. 模板注入漏洞 (Template Injection) - 高危
- **文件**: `template_injection_poc.py`
- **目标**: `app/Http/Controllers/DemoController.php:22`
- **风险**: 服务器端模板注入(SSTI)，可能导致远程代码执行
- **测试内容**:
  - 基本PHP代码执行测试
  - Laravel特定的模板注入
  - 文件系统操作尝试
  - 高级绕过技术

### 2. 文件操作漏洞 (File Operation Vulnerabilities) - 高危
- **文件**: `file_operation_poc.py`
- **目标**: 
  - `app/Http/Controllers/DemoController.php:32` (文件移动)
  - `app/Http/Controllers/DemoController.php:25` (文件上传)
- **风险**: 任意文件上传、移动和路径遍历
- **测试内容**:
  - 恶意文件上传测试
  - 任意文件移动测试
  - 目录遍历攻击
  - 文件访问验证

### 3. 认证绕过漏洞 (Authentication Bypass) - 中危
- **文件**: `auth_bypass_poc.py`
- **目标**: `app/Http/Middleware/EnsureTokenIsValid.php:19`
- **风险**: 硬编码认证令牌可被绕过
- **测试内容**:
  - 硬编码token测试
  - Token暴力破解
  - Header变体测试
  - 大小写敏感性测试

### 4. LESS代码注入漏洞 (LESS Code Injection) - 中危
- **文件**: `less_injection_poc.py`
- **目标**: `app/Http/Controllers/CssController.php:18`
- **风险**: LESS编译器代码注入和文件包含
- **测试内容**:
  - LESS语法注入
  - 文件包含尝试
  - 拒绝服务(DoS)攻击

## 🚀 使用方法

### 环境要求
```bash
# Python 3.6+
pip install requests
```

### 1. 单独运行各个测试脚本

#### 模板注入测试
```bash
python3 template_injection_poc.py http://localhost:8080
```

#### 文件操作测试
```bash
python3 file_operation_poc.py http://localhost:8080
```

#### 认证绕过测试
```bash
python3 auth_bypass_poc.py http://localhost:8080
```

#### LESS代码注入测试
```bash
python3 less_injection_poc.py http://localhost:8080
```

### 2. 运行综合测试 (推荐)

#### 完整测试
```bash
python3 comprehensive_security_test.py http://localhost:8080
```

#### 快速扫描 (仅测试关键漏洞)
```bash
python3 comprehensive_security_test.py http://localhost:8080 --quick
```

#### 保存结果到文件
```bash
python3 comprehensive_security_test.py http://localhost:8080 -o results.json
```

## 📊 测试报告示例

```
================================================================================
📊 综合安全测试报告
================================================================================
🎯 目标: http://localhost:8080
📅 测试时间: 2024-01-15T10:30:00
⚠️  总体风险等级: 🔴 极高风险 (评分: 85/100)

🔴 高危 模板注入: 3/8 个payload成功
🔴 高危 文件操作: 上传(2) 移动(1) 遍历(1)
🟡 中危 认证绕过: 绕过(1) 令牌(1)
🟡 中危 LESS注入: 包含(0) DoS(1)

🔧 修复优先级建议:
  1. 🔴 立即修复模板注入漏洞 - 可能导致远程代码执行
  2. 🟠 修复文件操作漏洞 - 可能导致任意文件上传/移动
  3. 🟡 修复认证绕过漏洞 - 更换硬编码令牌
  4. 🟡 修复LESS注入漏洞 - 限制文件包含功能
```

## 🛡️ 修复建议

### 模板注入修复
```php
// 错误的做法
return Blade::render("Hello, $template");

// 正确的做法
return view('demo', ['name' => e($template)]);
```

### 文件操作修复
```php
// 添加路径验证
if (strpos($file, '..') !== false) {
    abort(400, 'Invalid path');
}

// 文件类型白名单
$allowedTypes = ['jpg', 'png', 'gif', 'pdf'];
if (!in_array($extension, $allowedTypes)) {
    abort(400, 'File type not allowed');
}
```

### 认证修复
```php
// 使用环境变量
if ($request->header('X-Token') !== env('API_SECRET_TOKEN')) {
    return response()->json(['message' => 'Unauthorized'], 401);
}
```

### LESS注入修复
```php
// 禁用导入功能
$compiler = new Less_Parser(['import_dirs' => []]);

// 添加输入验证
if (strlen($lessCode) > 10000) {
    abort(400, 'Code too long');
}
```

## 📁 文件结构

```
security_tests/
├── README.md                          # 本文件
├── comprehensive_security_test.py     # 综合测试脚本
├── template_injection_poc.py          # 模板注入测试
├── file_operation_poc.py              # 文件操作测试
├── auth_bypass_poc.py                 # 认证绕过测试
└── less_injection_poc.py              # LESS代码注入测试
```

## 🔧 自定义配置

### 修改测试参数
可以在各个脚本中修改以下参数：
- 超时时间
- 测试payload
- 请求头设置
- 输出格式

### 添加新的测试用例
在相应的测试类中添加新的测试方法，并更新payload列表。

## ⚡ 性能优化

- 使用 `--quick` 参数进行快速扫描
- 调整请求间隔避免被限制
- 使用多线程 (需要修改代码)

## 🐛 故障排除

### 常见问题

1. **连接超时**
   - 检查目标URL是否正确
   - 确认应用是否正在运行
   - 调整超时设置

2. **导入错误**
   - 确保所有脚本在同一目录
   - 检查Python路径设置

3. **权限错误**
   - 确保有文件写入权限
   - 检查目录权限设置

## 📝 更新日志

- **v1.0** - 初始版本，支持4种主要漏洞测试
- 支持综合测试和单独测试
- 添加快速扫描功能
- 支持JSON格式输出

## 🤝 贡献

欢迎提交问题报告和改进建议。请确保：
- 遵循代码规范
- 添加适当的测试
- 更新文档

## 📄 许可证

本工具仅用于教育和授权的安全测试目的。使用者需遵守相关法律法规。

---

**再次提醒**: 请仅在获得明确授权的系统上使用这些工具！
