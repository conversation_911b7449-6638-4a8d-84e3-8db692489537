<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Blade;
use Exception;
use Less_Parser;
use ParseError;

class CssController extends Controller
{
    public function compileFile(Request $request) {
        try {
            $compiler = new Less_Parser();
            $lessCode = $request->input("code");
            $path = resource_path('css/app.css');
            $css = $compiler->parse(file_get_contents($path) .$lessCode);
            $cssCode =  $css->getCss();
            $outPath = resource_path('css/temp.css');
            file_put_contents($outPath, $cssCode);
            return Blade::render("编译成功: 'temp.css'\n");
        } catch (ParseError $e) {
            return Blade::render("编译错误: " . $e->getMessage() . "\n");
        }
    }

    public function getCss() {
        $path = resource_path("css/temp.css");
        if(!file_exists($path)) {
            echo "文件不存在: css/temp.css";
            exit();
        }

        try {
            $tempCode = file_get_contents($path);
            return Blade::render($tempCode);
        } catch (Exception $e) {
            return Blade::render("error");
        }
    }
}
