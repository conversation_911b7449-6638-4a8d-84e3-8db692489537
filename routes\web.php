<?php

use App\Http\Controllers\CssController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DemoController;
use App\Http\Middleware\EnsureTokenIsValid;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});
Route::post('/demo', [DemoController::class, 'index']);
Route::post('/compileCss', [CssController::class, 'compileFile']);
Route::get('/readFile', [CssController::class, 'getCss']);
Route::get('/token',[DemoController::class, 'token']);
Route::post('/upload',[DemoController::class, 'uploadFile']);
Route::post('/move', [DemoController::class, 'move']);

