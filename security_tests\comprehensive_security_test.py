#!/usr/bin/env python3
"""
综合安全测试脚本
整合所有发现的安全漏洞的测试功能
仅用于授权的安全测试目的

支持的漏洞测试:
1. 模板注入 (Template Injection)
2. 文件操作漏洞 (File Operation Vulnerabilities)
3. 认证绕过 (Authentication Bypass)
4. LESS代码注入 (LESS Code Injection)
"""

import sys
import os
import argparse
import json
import time
from datetime import datetime

# 导入各个测试模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from template_injection_poc import TemplateInjectionTester
    from file_operation_poc import FileOperationTester
    from auth_bypass_poc import AuthBypassTester
    from less_injection_poc import LessInjectionTester
except ImportError as e:
    print(f"[!] 导入测试模块失败: {e}")
    print("请确保所有PoC脚本都在同一目录下")
    sys.exit(1)

class ComprehensiveSecurityTester:
    def __init__(self, target_url, output_file=None):
        self.target_url = target_url.rstrip('/')
        self.output_file = output_file
        self.results = {
            'target': target_url,
            'timestamp': datetime.now().isoformat(),
            'tests': {}
        }
        
        # 初始化各个测试器
        self.template_tester = TemplateInjectionTester(target_url)
        self.file_tester = FileOperationTester(target_url)
        self.auth_tester = AuthBypassTester(target_url)
        self.less_tester = LessInjectionTester(target_url)
    
    def run_all_tests(self):
        """运行所有安全测试"""
        print("="*80)
        print("Laravel 应用综合安全测试")
        print("仅用于授权的安全测试目的")
        print("="*80)
        print(f"目标URL: {self.target_url}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 1. 模板注入测试
        print("\n🔍 [1/4] 执行模板注入漏洞测试...")
        try:
            template_results = self._run_template_injection_tests()
            self.results['tests']['template_injection'] = template_results
            print("✅ 模板注入测试完成")
        except Exception as e:
            print(f"❌ 模板注入测试失败: {e}")
            self.results['tests']['template_injection'] = {'error': str(e)}
        
        # 2. 文件操作漏洞测试
        print("\n🔍 [2/4] 执行文件操作漏洞测试...")
        try:
            file_results = self._run_file_operation_tests()
            self.results['tests']['file_operations'] = file_results
            print("✅ 文件操作测试完成")
        except Exception as e:
            print(f"❌ 文件操作测试失败: {e}")
            self.results['tests']['file_operations'] = {'error': str(e)}
        
        # 3. 认证绕过测试
        print("\n🔍 [3/4] 执行认证绕过漏洞测试...")
        try:
            auth_results = self._run_auth_bypass_tests()
            self.results['tests']['auth_bypass'] = auth_results
            print("✅ 认证绕过测试完成")
        except Exception as e:
            print(f"❌ 认证绕过测试失败: {e}")
            self.results['tests']['auth_bypass'] = {'error': str(e)}
        
        # 4. LESS代码注入测试
        print("\n🔍 [4/4] 执行LESS代码注入漏洞测试...")
        try:
            less_results = self._run_less_injection_tests()
            self.results['tests']['less_injection'] = less_results
            print("✅ LESS代码注入测试完成")
        except Exception as e:
            print(f"❌ LESS代码注入测试失败: {e}")
            self.results['tests']['less_injection'] = {'error': str(e)}
        
        # 生成综合报告
        self._generate_comprehensive_report()
        
        # 保存结果到文件
        if self.output_file:
            self._save_results_to_file()
    
    def _run_template_injection_tests(self):
        """运行模板注入测试"""
        basic_results = self.template_tester.test_basic_injection()
        advanced_results = self.template_tester.test_advanced_injection()
        
        return {
            'basic_tests': basic_results,
            'advanced_tests': advanced_results,
            'summary': {
                'total_tests': len(basic_results) + len(advanced_results),
                'vulnerable_tests': sum(1 for r in basic_results + advanced_results if r['vulnerable'])
            }
        }
    
    def _run_file_operation_tests(self):
        """运行文件操作测试"""
        upload_results = self.file_tester.test_file_upload_vulnerabilities()
        move_results = self.file_tester.test_file_move_vulnerabilities()
        traversal_results = self.file_tester.test_directory_traversal()
        
        return {
            'upload_tests': upload_results,
            'move_tests': move_results,
            'traversal_tests': traversal_results,
            'summary': {
                'successful_uploads': sum(1 for r in upload_results if r['success']),
                'successful_moves': sum(1 for r in move_results if r['success']),
                'potential_traversals': sum(1 for r in traversal_results if r['potential_vulnerability'])
            }
        }
    
    def _run_auth_bypass_tests(self):
        """运行认证绕过测试"""
        bypass_results = self.auth_tester.test_hardcoded_token_bypass()
        bruteforce_results = self.auth_tester.test_token_bruteforce()
        header_results = self.auth_tester.test_header_variations()
        case_results = self.auth_tester.test_case_sensitivity()
        
        return {
            'bypass_tests': bypass_results,
            'bruteforce_tests': bruteforce_results,
            'header_tests': header_results,
            'case_tests': case_results,
            'summary': {
                'successful_bypasses': sum(1 for r in bypass_results if r['bypass_successful']),
                'successful_tokens': sum(1 for r in bruteforce_results if r['success']),
                'successful_headers': sum(1 for r in header_results if r['success']),
                'successful_cases': sum(1 for r in case_results if r['success'])
            }
        }
    
    def _run_less_injection_tests(self):
        """运行LESS代码注入测试"""
        syntax_results = self.less_tester.test_less_syntax_injection()
        inclusion_results = self.less_tester.test_file_inclusion_attempts()
        dos_results = self.less_tester.test_dos_attacks()
        
        return {
            'syntax_tests': syntax_results,
            'inclusion_tests': inclusion_results,
            'dos_tests': dos_results,
            'summary': {
                'successful_compilations': sum(1 for r in syntax_results if r['compilation_success']),
                'potential_inclusions': sum(1 for r in inclusion_results if r['potential_inclusion']),
                'potential_dos': sum(1 for r in dos_results if r['potential_dos'])
            }
        }
    
    def _generate_comprehensive_report(self):
        """生成综合安全测试报告"""
        print("\n" + "="*80)
        print("📊 综合安全测试报告")
        print("="*80)
        
        # 计算总体风险评分
        risk_score = self._calculate_risk_score()
        risk_level = self._get_risk_level(risk_score)
        
        print(f"🎯 目标: {self.target_url}")
        print(f"📅 测试时间: {self.results['timestamp']}")
        print(f"⚠️  总体风险等级: {risk_level} (评分: {risk_score}/100)")
        print()
        
        # 各项测试结果摘要
        self._print_test_summary("模板注入", self.results['tests'].get('template_injection', {}))
        self._print_test_summary("文件操作", self.results['tests'].get('file_operations', {}))
        self._print_test_summary("认证绕过", self.results['tests'].get('auth_bypass', {}))
        self._print_test_summary("LESS注入", self.results['tests'].get('less_injection', {}))
        
        # 修复优先级建议
        print("\n🔧 修复优先级建议:")
        priorities = self._get_fix_priorities()
        for i, priority in enumerate(priorities, 1):
            print(f"  {i}. {priority}")
        
        print("\n" + "="*80)
    
    def _print_test_summary(self, test_name, test_results):
        """打印单项测试摘要"""
        if 'error' in test_results:
            print(f"❌ {test_name}: 测试失败 - {test_results['error']}")
            return
        
        if 'summary' not in test_results:
            print(f"⚠️  {test_name}: 无摘要数据")
            return
        
        summary = test_results['summary']
        
        if test_name == "模板注入":
            vulnerable = summary.get('vulnerable_tests', 0)
            total = summary.get('total_tests', 0)
            status = "🔴 高危" if vulnerable > 0 else "🟢 安全"
            print(f"{status} {test_name}: {vulnerable}/{total} 个payload成功")
        
        elif test_name == "文件操作":
            uploads = summary.get('successful_uploads', 0)
            moves = summary.get('successful_moves', 0)
            traversals = summary.get('potential_traversals', 0)
            total_issues = uploads + moves + traversals
            status = "🔴 高危" if total_issues > 0 else "🟢 安全"
            print(f"{status} {test_name}: 上传({uploads}) 移动({moves}) 遍历({traversals})")
        
        elif test_name == "认证绕过":
            bypasses = summary.get('successful_bypasses', 0)
            tokens = summary.get('successful_tokens', 0)
            total_issues = bypasses + tokens
            status = "🟡 中危" if total_issues > 0 else "🟢 安全"
            print(f"{status} {test_name}: 绕过({bypasses}) 令牌({tokens})")
        
        elif test_name == "LESS注入":
            inclusions = summary.get('potential_inclusions', 0)
            dos = summary.get('potential_dos', 0)
            total_issues = inclusions + dos
            status = "🟡 中危" if total_issues > 0 else "🟢 安全"
            print(f"{status} {test_name}: 包含({inclusions}) DoS({dos})")
    
    def _calculate_risk_score(self):
        """计算风险评分 (0-100)"""
        score = 0
        
        # 模板注入 (最高风险 40分)
        template_results = self.results['tests'].get('template_injection', {})
        if 'summary' in template_results:
            vulnerable = template_results['summary'].get('vulnerable_tests', 0)
            if vulnerable > 0:
                score += min(40, vulnerable * 10)
        
        # 文件操作 (高风险 30分)
        file_results = self.results['tests'].get('file_operations', {})
        if 'summary' in file_results:
            summary = file_results['summary']
            file_issues = (summary.get('successful_uploads', 0) + 
                          summary.get('successful_moves', 0) + 
                          summary.get('potential_traversals', 0))
            if file_issues > 0:
                score += min(30, file_issues * 5)
        
        # 认证绕过 (中风险 20分)
        auth_results = self.results['tests'].get('auth_bypass', {})
        if 'summary' in auth_results:
            summary = auth_results['summary']
            auth_issues = (summary.get('successful_bypasses', 0) + 
                          summary.get('successful_tokens', 0))
            if auth_issues > 0:
                score += min(20, auth_issues * 3)
        
        # LESS注入 (中风险 10分)
        less_results = self.results['tests'].get('less_injection', {})
        if 'summary' in less_results:
            summary = less_results['summary']
            less_issues = (summary.get('potential_inclusions', 0) + 
                          summary.get('potential_dos', 0))
            if less_issues > 0:
                score += min(10, less_issues * 2)
        
        return min(100, score)
    
    def _get_risk_level(self, score):
        """根据评分获取风险等级"""
        if score >= 70:
            return "🔴 极高风险"
        elif score >= 50:
            return "🟠 高风险"
        elif score >= 30:
            return "🟡 中等风险"
        elif score >= 10:
            return "🟢 低风险"
        else:
            return "✅ 相对安全"
    
    def _get_fix_priorities(self):
        """获取修复优先级建议"""
        priorities = []
        
        # 检查各种漏洞并按优先级排序
        template_results = self.results['tests'].get('template_injection', {})
        if 'summary' in template_results and template_results['summary'].get('vulnerable_tests', 0) > 0:
            priorities.append("🔴 立即修复模板注入漏洞 - 可能导致远程代码执行")
        
        file_results = self.results['tests'].get('file_operations', {})
        if 'summary' in file_results:
            summary = file_results['summary']
            if summary.get('successful_uploads', 0) > 0 or summary.get('successful_moves', 0) > 0:
                priorities.append("🟠 修复文件操作漏洞 - 可能导致任意文件上传/移动")
        
        auth_results = self.results['tests'].get('auth_bypass', {})
        if 'summary' in auth_results:
            summary = auth_results['summary']
            if summary.get('successful_bypasses', 0) > 0:
                priorities.append("🟡 修复认证绕过漏洞 - 更换硬编码令牌")
        
        less_results = self.results['tests'].get('less_injection', {})
        if 'summary' in less_results:
            summary = less_results['summary']
            if summary.get('potential_inclusions', 0) > 0:
                priorities.append("🟡 修复LESS注入漏洞 - 限制文件包含功能")
        
        if not priorities:
            priorities.append("✅ 未发现严重安全漏洞，建议进行代码审查")
        
        return priorities

    def _save_results_to_file(self):
        """保存结果到JSON文件"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 测试结果已保存到: {self.output_file}")
        except Exception as e:
            print(f"\n❌ 保存结果失败: {e}")

    def run_quick_scan(self):
        """运行快速扫描 - 仅测试最关键的漏洞"""
        print("⚡ 执行快速安全扫描...")

        # 只测试最关键的漏洞
        try:
            # 测试模板注入 (最危险)
            basic_template = self.template_tester.test_basic_injection()[:3]  # 只测试前3个
            self.results['tests']['template_injection'] = {
                'basic_tests': basic_template,
                'summary': {'vulnerable_tests': sum(1 for r in basic_template if r['vulnerable'])}
            }

            # 测试认证绕过
            bypass_results = self.auth_tester.test_hardcoded_token_bypass()
            self.results['tests']['auth_bypass'] = {
                'bypass_tests': bypass_results,
                'summary': {'successful_bypasses': sum(1 for r in bypass_results if r['bypass_successful'])}
            }

            print("✅ 快速扫描完成")
            self._generate_comprehensive_report()

        except Exception as e:
            print(f"❌ 快速扫描失败: {e}")

def main():
    parser = argparse.ArgumentParser(
        description='Laravel应用综合安全测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 comprehensive_security_test.py http://localhost:8080
  python3 comprehensive_security_test.py http://localhost:8080 -o results.json
  python3 comprehensive_security_test.py http://localhost:8080 --quick

注意: 此工具仅用于授权的安全测试目的
        """
    )

    parser.add_argument('target_url', help='目标应用URL')
    parser.add_argument('-o', '--output', help='输出结果到JSON文件')
    parser.add_argument('--quick', action='store_true', help='执行快速扫描 (仅测试关键漏洞)')

    args = parser.parse_args()

    # 验证URL格式
    if not args.target_url.startswith(('http://', 'https://')):
        print("❌ 错误: URL必须以http://或https://开头")
        sys.exit(1)

    # 创建测试器并运行测试
    tester = ComprehensiveSecurityTester(args.target_url, args.output)

    if args.quick:
        tester.run_quick_scan()
    else:
        tester.run_all_tests()

if __name__ == "__main__":
    main()
