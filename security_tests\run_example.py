#!/usr/bin/env python3
"""
安全测试工具使用示例
演示如何使用各个安全测试脚本
仅用于授权的安全测试目的
"""

import os
import sys
import subprocess
import time

def print_banner():
    """打印横幅"""
    print("="*70)
    print("Laravel 应用安全测试工具 - 使用示例")
    print("仅用于授权的安全测试目的")
    print("="*70)

def check_target_availability(target_url):
    """检查目标是否可访问"""
    print(f"[*] 检查目标可用性: {target_url}")
    
    try:
        import requests
        response = requests.get(target_url, timeout=10)
        if response.status_code == 200:
            print("[+] 目标可访问")
            return True
        else:
            print(f"[-] 目标返回状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"[-] 目标不可访问: {e}")
        return False

def run_individual_tests(target_url):
    """运行单独的测试脚本"""
    print("\n" + "="*50)
    print("运行单独测试脚本")
    print("="*50)
    
    tests = [
        {
            'name': '模板注入测试',
            'script': 'template_injection_poc.py',
            'description': '测试Blade模板注入漏洞'
        },
        {
            'name': '文件操作测试',
            'script': 'file_operation_poc.py',
            'description': '测试文件上传和移动漏洞'
        },
        {
            'name': '认证绕过测试',
            'script': 'auth_bypass_poc.py',
            'description': '测试硬编码token认证绕过'
        },
        {
            'name': 'LESS代码注入测试',
            'script': 'less_injection_poc.py',
            'description': '测试LESS编译器代码注入'
        }
    ]
    
    for i, test in enumerate(tests, 1):
        print(f"\n[{i}/4] {test['name']}")
        print(f"描述: {test['description']}")
        
        # 询问是否运行此测试
        choice = input(f"是否运行此测试? (y/n/s=跳过所有): ").lower().strip()
        
        if choice == 's':
            print("跳过剩余的单独测试")
            break
        elif choice == 'y':
            try:
                print(f"[*] 运行 {test['script']}...")
                result = subprocess.run([
                    sys.executable, test['script'], target_url
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print("[+] 测试完成")
                    # 显示部分输出
                    output_lines = result.stdout.split('\n')
                    for line in output_lines[-10:]:  # 显示最后10行
                        if line.strip():
                            print(f"    {line}")
                else:
                    print(f"[-] 测试失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print("[!] 测试超时")
            except FileNotFoundError:
                print(f"[!] 找不到脚本文件: {test['script']}")
            except Exception as e:
                print(f"[!] 运行测试时出错: {e}")
        else:
            print("跳过此测试")
        
        time.sleep(1)  # 短暂延迟

def run_comprehensive_test(target_url):
    """运行综合测试"""
    print("\n" + "="*50)
    print("运行综合安全测试")
    print("="*50)
    
    print("综合测试选项:")
    print("1. 完整测试 (所有漏洞)")
    print("2. 快速扫描 (仅关键漏洞)")
    print("3. 保存结果到文件")
    
    choice = input("请选择测试类型 (1/2/3): ").strip()
    
    cmd = [sys.executable, 'comprehensive_security_test.py', target_url]
    
    if choice == '2':
        cmd.append('--quick')
        print("[*] 执行快速扫描...")
    elif choice == '3':
        output_file = f"security_test_results_{int(time.time())}.json"
        cmd.extend(['-o', output_file])
        print(f"[*] 执行完整测试，结果将保存到: {output_file}")
    else:
        print("[*] 执行完整测试...")
    
    try:
        result = subprocess.run(cmd, timeout=300)  # 5分钟超时
        
        if result.returncode == 0:
            print("[+] 综合测试完成")
        else:
            print("[-] 综合测试可能遇到问题")
            
    except subprocess.TimeoutExpired:
        print("[!] 综合测试超时")
    except FileNotFoundError:
        print("[!] 找不到综合测试脚本")
    except Exception as e:
        print(f"[!] 运行综合测试时出错: {e}")

def show_help():
    """显示帮助信息"""
    print("\n" + "="*50)
    print("帮助信息")
    print("="*50)
    
    help_text = """
使用方法:
1. 确保目标Laravel应用正在运行
2. 运行此示例脚本: python3 run_example.py
3. 按照提示选择测试类型

手动运行命令:
- 模板注入: python3 template_injection_poc.py <URL>
- 文件操作: python3 file_operation_poc.py <URL>
- 认证绕过: python3 auth_bypass_poc.py <URL>
- LESS注入: python3 less_injection_poc.py <URL>
- 综合测试: python3 comprehensive_security_test.py <URL>

注意事项:
- 仅在授权的系统上使用
- 建议在测试环境中运行
- 某些测试可能会创建文件或修改系统状态
- 测试完成后请清理测试数据

风险等级:
🔴 高危: 模板注入、文件操作
🟡 中危: 认证绕过、LESS注入
    """
    
    print(help_text)

def main():
    print_banner()
    
    # 获取目标URL
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
    else:
        target_url = input("请输入目标URL (例: http://localhost:8080): ").strip()
    
    if not target_url:
        print("❌ 错误: 必须提供目标URL")
        sys.exit(1)
    
    if not target_url.startswith(('http://', 'https://')):
        print("❌ 错误: URL必须以http://或https://开头")
        sys.exit(1)
    
    # 检查目标可用性
    if not check_target_availability(target_url):
        choice = input("目标似乎不可访问，是否继续? (y/n): ").lower().strip()
        if choice != 'y':
            print("测试已取消")
            sys.exit(1)
    
    # 主菜单
    while True:
        print("\n" + "="*50)
        print("请选择测试类型:")
        print("1. 运行单独测试脚本")
        print("2. 运行综合安全测试")
        print("3. 显示帮助信息")
        print("4. 退出")
        print("="*50)
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            run_individual_tests(target_url)
        elif choice == '2':
            run_comprehensive_test(target_url)
        elif choice == '3':
            show_help()
        elif choice == '4':
            print("感谢使用安全测试工具!")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        # 询问是否继续
        if choice in ['1', '2']:
            continue_choice = input("\n是否继续其他测试? (y/n): ").lower().strip()
            if continue_choice != 'y':
                print("测试完成，感谢使用!")
                break

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[!] 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n[!] 程序异常: {e}")
        sys.exit(1)
